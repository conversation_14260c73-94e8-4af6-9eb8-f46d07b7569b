
# Bad Breath Attack 👾

![Bad Breath Attack](assets/images/bad_breath_attack_splash_screen.jpg)
 

## Description

This project is a web-based game - a tiny shooter, but a little different. Explore the world, shoot enemies, and discover the unique gameplay mechanics.

## Features

*   Lightweight and fast, designed for web browsers.
*   Unique gameplay mechanics that set it apart from other shooters.
*   Built with JavaScript and Babylon.js for cross-platform compatibility.
*   Open-source and MIT licensed.

## Installation

### Prerequisites

*   A modern web browser (Chrome, Firefox, Safari, Edge, etc.)
*   A web server (for local development).  You can use tools like `http-server` (Node.js), Python's `http.server`, or any other web server.

### Steps

1.  Clone the repository:

    ```bash
    git clone https://github.com/Emaj7th/tinyshootergame.git
    cd tinyshootergame
    ```

2.  Serve the project files using a web server.  For example, using `http-server` (requires Node.js):

    ```bash
    # If you don't have http-server installed:
    # npm install -g http-server

    http-server .
    ```

3.  Open your web browser and navigate to the address provided by the web server (usually `http://localhost:8080` or similar).

## Contributing

Contributions are welcome! If you'd like to contribute to the project, please follow these guidelines:

1.  Fork the repository.
2.  Create a new branch for your feature or bug fix.
3.  Make your changes and commit them with clear, descriptive messages.
4.  Submit a pull request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
